# Beta环境配置
spring:
  datasource:
    url: *******************************************************************************************************************************************
    username: root
    password: e_,HUH58FT
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 1800000
      leak-detection-threshold: 5000
      pool-name: HikariCP-Beta
      auto-commit: true
      connection-test-query: SELECT 1

  data:
    redis:
      database: 0
      host: redis
      password: E6I6ABAOrEWntV9HaF6C
      port: 6379
      timeout: 6000ms
      jedis:
        pool:
          max-active: 50
          max-idle: 20
          min-idle: 5
          max-wait: -1ms

  security:
    user:
      name: swagger
      password: 2gz9AbJBciinsurvVGd0
      roles: ADMIN

# 日志配置
logging:
  level:
    root: warn
    com.iot.platform: info

# 自定义配置
iot:
  security:
    ak-sk:
      enabled: false  # 临时关闭
  mqtt:
    broker-url: tcp://mqtt:1883
