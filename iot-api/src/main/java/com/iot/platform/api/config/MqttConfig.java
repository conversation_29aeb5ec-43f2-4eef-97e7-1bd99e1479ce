package com.iot.platform.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

/**
 * MQTT配置类
 * 支持MQTT over WebSocket双向通信
 * 用于边缘程序与服务端的实时通信
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Configuration
@ConfigurationProperties(prefix = "iot.mqtt")
@Data
public class MqttConfig {

//    /**
//     * MQTT Broker地址
//     */
//    private String brokerUrl = "tcp://localhost:1883";
//
//    /**
//     * WebSocket地址（用于Web客户端）
//     */
//    private String webSocketUrl = "ws://localhost:8083/mqtt";
//
//    /**
//     * 客户端ID前缀
//     */
//    private String clientIdPrefix = "iot-platform";
//
//    /**
//     * 用户名
//     */
//    private String username = "iot_user";
//
//    /**
//     * 密码
//     */
//    private String password = "iot_pass";
//
//    /**
//     * 连接超时时间（秒）
//     */
//    private int connectionTimeout = 30;
//
//    /**
//     * 心跳间隔（秒）
//     */
//    private int keepAliveInterval = 60;
//
//    /**
//     * 自动重连
//     */
//    private boolean automaticReconnect = true;
//
//    /**
//     * 清除会话
//     */
//    private boolean cleanSession = true;
//
//    /**
//     * QoS等级
//     */
//    private int qos = 1;
//
//    /**
//     * 订阅主题
//     */
//    private String[] subscribeTopics = {
//        "iot/edge/+/heartbeat",     // 边缘程序心跳
//        "iot/edge/+/data",          // 设备数据上报
//        "iot/edge/+/status",        // 边缘程序状态
//        "iot/device/+/status",      // 设备状态变化
//        "iot/command/+/response"    // 指令执行响应
//    };
//
//    /**
//     * 发布主题前缀
//     */
//    private String publishTopicPrefix = "iot/platform";
//
//    /**
//     * MQTT客户端工厂
//     */
//    @Bean
//    public MqttPahoClientFactory mqttClientFactory() {
//        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
//        MqttConnectOptions options = new MqttConnectOptions();
//        options.setServerURIs(new String[]{brokerUrl});
//        options.setUserName(username);
//        options.setPassword(password.toCharArray());
//        options.setConnectionTimeout(connectionTimeout);
//        options.setKeepAliveInterval(keepAliveInterval);
//        options.setAutomaticReconnect(automaticReconnect);
//        options.setCleanSession(cleanSession);
//        factory.setConnectionOptions(options);
//        return factory;
//    }
//
//    /**
//     * MQTT输入通道
//     */
//    @Bean
//    public MessageChannel mqttInputChannel() {
//        return new DirectChannel();
//    }
//
//    /**
//     * MQTT输出通道
//     */
//    @Bean
//    public MessageChannel mqttOutputChannel() {
//        return new DirectChannel();
//    }
//
//    /**
//     * MQTT消息生产者（订阅消息）
//     */
//    @Bean
//    public MessageProducer inbound() {
//        MqttPahoMessageDrivenChannelAdapter adapter =
//                new MqttPahoMessageDrivenChannelAdapter(
//                        clientIdPrefix + "-inbound",
//                        mqttClientFactory(),
//                        subscribeTopics
//                );
//        adapter.setCompletionTimeout(5000);
//        adapter.setConverter(new DefaultPahoMessageConverter());
//        adapter.setQos(qos);
//        adapter.setOutputChannel(mqttInputChannel());
//        return adapter;
//    }
//
//    /**
//     * MQTT消息处理器（发布消息）
//     */
//    @Bean
//    @ServiceActivator(inputChannel = "mqttOutputChannel")
//    public MessageHandler mqttOutbound() {
//        MqttPahoMessageHandler messageHandler =
//                new MqttPahoMessageHandler(
//                        clientIdPrefix + "-outbound",
//                        mqttClientFactory()
//                );
//        messageHandler.setAsync(true);
//        messageHandler.setDefaultQos(qos);
//        messageHandler.setDefaultRetained(false);
//        return messageHandler;
//    }
//
//    /**
//     * MQTT消息接收处理器
//     */
//    @Bean
//    @ServiceActivator(inputChannel = "mqttInputChannel")
//    public MessageHandler mqttMessageHandler() {
//        return message -> {
//            String topic = message.getHeaders().get("mqtt_receivedTopic", String.class);
//            String payload = message.getPayload().toString();
//
//            // 根据主题类型处理不同的消息
//            if (topic != null) {
//                if (topic.contains("/heartbeat")) {
//                    handleHeartbeatMessage(topic, payload);
//                } else if (topic.contains("/data")) {
//                    handleDataMessage(topic, payload);
//                } else if (topic.contains("/status")) {
//                    handleStatusMessage(topic, payload);
//                } else if (topic.contains("/response")) {
//                    handleCommandResponse(topic, payload);
//                }
//            }
//        };
//    }
//
//    /**
//     * 处理心跳消息
//     */
//    private void handleHeartbeatMessage(String topic, String payload) {
//        // TODO: 实现心跳消息处理逻辑
//        System.out.println("收到心跳消息 - Topic: " + topic + ", Payload: " + payload);
//    }
//
//    /**
//     * 处理数据消息
//     */
//    private void handleDataMessage(String topic, String payload) {
//        // TODO: 实现数据消息处理逻辑
//        System.out.println("收到数据消息 - Topic: " + topic + ", Payload: " + payload);
//    }
//
//    /**
//     * 处理状态消息
//     */
//    private void handleStatusMessage(String topic, String payload) {
//        // TODO: 实现状态消息处理逻辑
//        System.out.println("收到状态消息 - Topic: " + topic + ", Payload: " + payload);
//    }
//
//    /**
//     * 处理指令响应
//     */
//    private void handleCommandResponse(String topic, String payload) {
//        // TODO: 实现指令响应处理逻辑
//        System.out.println("收到指令响应 - Topic: " + topic + ", Payload: " + payload);
//    }
}
