# IoT API服务 Dockerfile - 使用Maven缓存卷
# 需要配合 --mount 参数使用

# 构建阶段
FROM maven:3.8.5-openjdk-17-slim AS builder

# 设置工作目录
WORKDIR /app

# 创建简化的父pom.xml（只包含iot-common和iot-api）
COPY pom.xml pom-original.xml
RUN sed '/<module>iot-edge<\/module>/d; /<module>iot-compute<\/module>/d' pom-original.xml > pom.xml

# 复制Maven配置文件
COPY iot-common/pom.xml iot-common/
COPY iot-api/pom.xml iot-api/

# 复制源代码
COPY iot-common/src iot-common/src
COPY iot-api/src iot-api/src

# 构建应用（使用缓存卷时，Maven仓库会被持久化）
RUN --mount=type=cache,target=/root/.m2 \
    mvn clean package -pl iot-api -am -DskipTests -B

# 运行阶段
FROM openjdk:17.0.1-jdk-slim

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装必要工具
RUN apt-get update && apt-get install -y \
    curl \
    netcat-traditional \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制构建产物
COPY --from=builder /app/iot-api/target/iot-api-*.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/actuator/health || exit 1

# 暴露端口
EXPOSE 8000

# JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS $JVM_OPTS -jar app.jar"]
